/**
 * 日誌記錄模組
 * 用於記錄系統事件，包括：
 * 1. 登入、登出
 * 2. 上傳台指/美股文字檔
 * 3. 新增用戶/刪除用戶
 */

const Logger = {
  /**
   * 生成 UTC+8 時間戳記
   * @returns {string} - UTC+8 時間戳記 (ISO 格式，但標示為 +08:00)
   */
  getUTC8Timestamp: function() {
    const now = new Date();
    // 創建 UTC+8 時間
    const utc8Time = new Date(now.getTime() + (8 * 60 * 60 * 1000));

    // 手動格式化為 UTC+8 時間字串
    const year = utc8Time.getUTCFullYear();
    const month = String(utc8Time.getUTCMonth() + 1).padStart(2, '0');
    const day = String(utc8Time.getUTCDate()).padStart(2, '0');
    const hours = String(utc8Time.getUTCHours()).padStart(2, '0');
    const minutes = String(utc8Time.getUTCMinutes()).padStart(2, '0');
    const seconds = String(utc8Time.getUTCSeconds()).padStart(2, '0');
    const milliseconds = String(utc8Time.getUTCMilliseconds()).padStart(3, '0');

    return `${year}-${month}-${day}T${hours}:${minutes}:${seconds}.${milliseconds}+08:00`;
  },

  /**
   * 記錄事件
   * @param {string} action - 事件類型 (login, logout, upload_taiex, upload_us, add_user, delete_user)
   * @param {object} details - 事件詳細資訊
   * @returns {Promise} - 記錄結果
   */
  logEvent: async function(action, details = {}) {
    try {
      // 獲取當前用戶資訊
      const currentUser = JSON.parse(sessionStorage.getItem('loginStatus') || 'null');
      const username = currentUser ? currentUser.username : '未登入用戶';
      
      // 構建日誌資料
      const logData = {
        timestamp: this.getUTC8Timestamp(),
        username: username,
        action: action,
        details: details
      };
      
      
      // 使用 XMLHttpRequest 發送日誌到後端
      return new Promise((resolve, reject) => {
        const xhr = new XMLHttpRequest();
        xhr.open('POST', 'https://bot.agatha-ai.com/flowise/eda63afd-8051-4aba-8eb0-e527fa6937a7/log', true);
        xhr.setRequestHeader('Content-Type', 'application/json');
        
        xhr.onload = function() {
          if (xhr.status >= 200 && xhr.status < 300) {
            try {
              const response = JSON.parse(xhr.responseText);
              resolve(response);
            } catch (e) {
              resolve({ success: true });
            }
          } else {
            console.error(xhr.status, xhr.statusText);
            // 如果 API 請求失敗，將日誌存儲在本地，稍後再嘗試發送
            this.storeLogLocally(action, details);
            reject(new Error(`日誌記錄失敗: ${xhr.status}`));
          }
        }.bind(this);
        
        xhr.onerror = function() {
          // 如果 API 請求失敗，將日誌存儲在本地，稍後再嘗試發送
          this.storeLogLocally(action, details);
          reject(new Error('網絡錯誤'));
        }.bind(this);
        
        xhr.send(JSON.stringify(logData));
      });
    } catch (error) {
      console.error(error);
      return { success: false, error: error.message };
    }
  },
  
  /**
   * 將日誌存儲在本地
   * @param {string} action - 事件類型
   * @param {object} details - 事件詳細資訊
   */
  storeLogLocally: function(action, details = {}) {
    try {
      // 獲取當前用戶資訊
      const currentUser = JSON.parse(sessionStorage.getItem('loginStatus') || 'null');
      const username = currentUser ? currentUser.username : '未登入用戶';
      
      // 構建日誌資料
      const logData = {
        timestamp: this.getUTC8Timestamp(),
        username: username,
        action: action,
        details: details
      };
      
      // 獲取現有的本地日誌
      const localLogs = JSON.parse(localStorage.getItem('pendingLogs') || '[]');
      
      // 添加新日誌
      localLogs.push(logData);
      
      // 存儲更新後的日誌
      localStorage.setItem('pendingLogs', JSON.stringify(localLogs));
      
    } catch (error) {
      console.error(error);
    }
  },
  
  /**
   * 嘗試發送本地存儲的日誌
   * @returns {Promise} - 發送結果
   */
  sendPendingLogs: async function() {
    try {
      // 獲取本地存儲的日誌
      const localLogs = JSON.parse(localStorage.getItem('pendingLogs') || '[]');
      
      if (localLogs.length === 0) {
        return { success: true, message: '沒有待處理的日誌' };
      }
      
      
      // 使用 XMLHttpRequest 發送日誌到後端
      return new Promise((resolve, reject) => {
        const xhr = new XMLHttpRequest();
        xhr.open('POST', 'https://bot.agatha-ai.com/flowise/eda63afd-8051-4aba-8eb0-e527fa6937a7/log/batch', true);
        xhr.setRequestHeader('Content-Type', 'application/json');
        
        xhr.onload = function() {
          if (xhr.status >= 200 && xhr.status < 300) {
            // 清除已發送的日誌
            localStorage.removeItem('pendingLogs');
            
            try {
              const response = JSON.parse(xhr.responseText);
              resolve(response);
            } catch (e) {
              resolve({ success: true });
            }
          } else {
            console.error(xhr.status, xhr.statusText);
            
            // 如果日誌太多，可以考慮只保留最近的 100 條
            if (localLogs.length > 100) {
              const trimmedLogs = localLogs.slice(-100);  // 只保留最後 100 條
              localStorage.setItem('pendingLogs', JSON.stringify(trimmedLogs));
            }
            
            reject(new Error(`批量日誌發送失敗: ${xhr.status}`));
          }
        };
        
        xhr.onerror = function() {
          console.error('發送待處理日誌失敗');
          
          // 如果日誌太多，可以考慮只保留最近的 100 條
          if (localLogs.length > 100) {
            const trimmedLogs = localLogs.slice(-100);  // 只保留最後 100 條
            localStorage.setItem('pendingLogs', JSON.stringify(trimmedLogs));
          }
          
          reject(new Error('網絡錯誤'));
        };
        
        xhr.send(JSON.stringify({ logs: localLogs }));
      });
    } catch (error) {
      console.error(error);
      return { success: false, error: error.message };
    }
  },
  
  /**
   * 登入事件日誌
   * @param {string} username - 用戶名
   * @returns {Promise} - 記錄結果
   */
  logLogin: function(username) {
    return this.logEvent('login', { username });
  },
  
  /**
   * 登出事件日誌
   * @returns {Promise} - 記錄結果
   */
  logLogout: function() {
    return this.logEvent('logout', {});
  },
  
  /**
   * 上傳台指文字檔事件日誌
   * @param {Array} files - 上傳的文件列表
   * @returns {Promise} - 記錄結果
   */
  logUploadTaiex: function(files) {
    const fileNames = Array.from(files).map(file => file.name);
    return this.logEvent('upload_taiex', { files: fileNames });
  },
  
  /**
   * 上傳美股文字檔事件日誌
   * @param {Array} files - 上傳的文件列表
   * @returns {Promise} - 記錄結果
   */
  logUploadUS: function(files) {
    const fileNames = Array.from(files).map(file => file.name);
    return this.logEvent('upload_us', { files: fileNames });
  },
  
  /**
   * 新增用戶事件日誌
   * @param {string} username - 新增的用戶名
   * @param {string} role - 用戶角色
   * @returns {Promise} - 記錄結果
   */
  logAddUser: function(username, role) {
    return this.logEvent('add_user', { 
      username, 
      role,
      details: '新增用戶'
    });
  },
  
  /**
   * 刪除用戶事件日誌
   * @param {string} username - 刪除的用戶名
   * @returns {Promise} - 記錄結果
   */
  logDeleteUser: function(username) {
    return this.logEvent('delete_user', { 
      username,
      details: '刪除用戶'
    });
  },
  
  /**
   * 更新用戶狀態事件日誌
   * @param {string} username - 用戶名
   * @param {boolean} status - 新狀態 (true: 啟用, false: 停用)
   * @returns {Promise} - 記錄結果
   */
  logUpdateUserStatus: function(username, status) {
    return this.logEvent('update_user_status', {
      username,
      status,
      details: status ? '啟用用戶' : '停用用戶'
    });
  },

  /**
   * 修改用戶角色事件日誌
   * @param {string} username - 用戶名
   * @param {string} role - 新角色 (admin: 管理員, user: 一般用戶)
   * @returns {Promise} - 記錄結果
   */
  logUpdateUserRole: function(username, role) {
    return this.logEvent('update_user_role', {
      username,
      role,
      details: `修改用戶角色為${role === 'admin' ? '管理員' : '一般用戶'}`
    });
  },

  /**
   * 更改密碼事件日誌
   * @returns {Promise} - 記錄結果
   */
  logChangePassword: function() {
    return this.logEvent('change_password', {
      details: '用戶更改密碼'
    });
  },

  /**
   * 匯出帳號資訊事件日誌
   * @returns {Promise} - 記錄結果
   */
  logExportUsers: function() {
    return this.logEvent('export_users', {
      details: '匯出帳號資訊'
    });
  },

  /**
   * 查詢日誌事件日誌
   * @param {object} filters - 查詢篩選條件
   * @returns {Promise} - 記錄結果
   */
  logQueryLogs: function(filters = {}) {
    return this.logEvent('query_logs', {
      details: '查詢系統日誌',
      filters: filters
    });
  },

  /**
   * 導出日誌事件日誌
   * @param {object} filters - 導出篩選條件
   * @returns {Promise} - 記錄結果
   */
  logExportLogs: function(filters = {}) {
    return this.logEvent('export_logs', {
      details: '導出系統日誌',
      filters: filters
    });
  }
};

// 頁面載入時檢查本地日誌
window.addEventListener('load', function() {
  // 延遲一段時間再檢查，確保頁面已完全載入
  setTimeout(() => {
    Logger.sendPendingLogs().then(result => {
      if (result.success) {
        console.log('success');
      }
    }).catch(error => {
      console.error(error);
    });
  }, 2000);  // 延遲 2 秒
});

// 導出 Logger 模組
window.Logger = Logger;