import re
from datetime import datetime, timedelta
from fastapi.responses import JSONResponse
from prompt import STOCK_INDEX, GET_TAIEX, GET_OTC, Sector_prompt, NEWS_SUMMARY
from LLM import OpenAiLLM
from utils.get_index import get_taiex, get_otc
from format import StockIndex, StrongWeekStack, NewsResponse, US_NEWS_SUM, TitleModel
from googlesearch import search
from urllib.parse import urlparse
import tldextract
from crawl4ai import AsyncWebCrawler


DOMAIN_TO_CHINESE = {
    'money.udn.com': '聯合新聞',
    'udn.com': '聯合新聞',
    'www.cnyes.com': '鉅亨',
    'cnyes.com': '鉅亨',
    'tw.stock.yahoo.com': 'Yahoo',
    'news.yahoo.com': 'Yahoo',
    'www.moneydj.com': 'MoneyDJ',
    'moneydj.com': 'MoneyDJ',
    'ctee.com.tw': '工商時報',
    'money.ctee.com.tw': '工商時報',
    'money.udn.com': '經濟日報',
    'money.sina.com.tw': '經濟日報',
    'www.chinatimes.com': '中時新聞',
    'ec.ltn.com.tw': '自由時報',
    'www.ltn.com.tw': '自由時報',
    'www.ettoday.net': 'ETtoday',
    'finance.ettoday.net': 'ETtoday',
    'www.setn.com': '三立新聞',
    'www.storm.mg': '風傳媒',
    'www.nownews.com': 'NOWnews',
    'www.anuefund.com': '鉅亨',
    'www.businesstoday.com.tw': '今周刊',
    'www.businessweekly.com.tw': '商業周刊',
    'www.cmoney.tw': 'CMoney',
    'www.mirrormedia.mg': '鏡週刊',
    'vocus.cc': 'Vocus',
    'aastocks.com': 'AAStocks',
    'tvb.com': 'TVBS'
}

def get_chinese_source(domain):
    """將完整 domain 轉換為對應中文名稱或主網域"""
    if domain in DOMAIN_TO_CHINESE:
        return DOMAIN_TO_CHINESE[domain]
    
    # 萃取主網域（如 www.ctee.com.tw → ctee.com.tw）
    ext = tldextract.extract(domain)
    root_domain = f"{ext.domain}.{ext.suffix}" if ext.suffix else domain
    return DOMAIN_TO_CHINESE.get(root_domain, root_domain)

from urllib.parse import urlparse

def search_first_url(query, wanted_source, max_results=10):
    """
    使用 advanced search 回傳符合指定來源的結果清單；
    如果沒有符合來源，則回傳第一筆結果。
    """

    allowed_sources = {wanted_source}
    results = []
    fallback_result = None

    try:
        for i, result in enumerate(search(query, num_results=max_results, lang="zh-TW", advanced=True)):
            domain = urlparse(result.url).netloc
            source = get_chinese_source(domain)

            # 儲存第一筆作為備案
            if i == 0:
                fallback_result = {
                    'title': result.title,
                    'url': result.url,
                    'source': source,
                    'domain': domain,
                    'description': result.description
                }

            if source in allowed_sources:
                results.append({
                    'title': result.title,
                    'url': result.url,
                    'source': source,
                    'domain': domain,
                    'description': result.description
                })

        if results:
            return results
        elif fallback_result:
            return [fallback_result]
        else:
            return None

    except Exception as e:
        print(f"搜尋錯誤: {e}")
        return None


# async def crawl_content(url):
#     """使用 crawl4ai 抓取網頁內容"""
#     try:
#         async with AsyncWebCrawler() as crawler:
#             result = await crawler.arun(url=url)
#             return result.markdown
#     except Exception as e:
#         print(f"抓取內容錯誤: {e}")
#         return None

async def crawl_content(url):
    """使用 crawl4ai 抓取網頁內容，若第一次失敗則重試一次"""
    for attempt in range(2):  # 最多嘗試兩次
        try:
            async with AsyncWebCrawler() as crawler:
                result = await crawler.arun(url=url)
                return result.markdown
        except Exception as e:
            print(f"第 {attempt + 1} 次抓取內容錯誤: {e}")
            if attempt == 1:  # 第二次也失敗
                return None


def clean_text(text):
    """移除控制字元（如 \x00-\x1F, \x7F）"""
    return re.sub(r'[\x00-\x1F\x7F]', '', text)

def transform1(data):
    output = []

    # 固定資訊
    output.append({
        "type": "text",
        "value": data["report_date"],
        "x": 288,
        "y": 58,
        "fontSize": 24,
        "fill": "#FFFFFF"
    })

    # output.append({
    #     "type": "text",
    #     "value": data["title"],
    #     "x": 152,
    #     "y": 110,
    #     "fontSize": 57,
    #     "fill": "#FFFFFF"
    # })

    output.append({
        "type": "text",
        "value": data["previous_date"]+"指數",
        "x": 191,
        "y": 165,
        "fontSize": 15,
        "fill": "#FFFFFF"
    })

    def get_color(trend):
        return "#23ECB1" if trend == "down" else "#FE7575"

    # weighted_index
    wi = data["weighted_index"]
    output.extend([
        {
            "type": "text",
            "value": str(wi["current_value"]),
            "x": 203,
            "y": 248,
            "fontSize": 26,
            "fill": "#FFFFFF"
        },
        {
            "type": "text",
            "value": f"{float(wi['point_change']):.2f}",
            "x": 204,
            "y": 277,
            "fontSize": 24,
            "fill": get_color(wi["point_trend"])
        },
        {
            "type": "image",
            "value": wi["point_trend"],
            "x": 85,
            "y": 270,
            "fontSize": None,
            "height": None
        },
        {
            "type": "text",
            "value": f"{float(wi['percent_change']):+.2f}%",
            "x": 204,
            "y": 305,
            "fontSize": 24,
            "fill": get_color(wi["percent_trend"])
        }
    ])

    # otc_index
    otc = data["otc_index"]
    output.extend([
        {
            "type": "text",
            "value": str(otc["current_value"]),
            "x": 203,
            "y": 395,
            "fontSize": 26,
            "fill": "#FFFFFF"
        },
        {
            "type": "text",
            "value": f"{float(otc['point_change']):.2f}",
            "x": 204,
            "y": 423,
            "fontSize": 24,
            "fill": get_color(otc["point_trend"])
        },
        {
            "type": "image",
            "value": otc["point_trend"],
            "x": 85,
            "y": 418,
            "fontSize": None,
            "height": None
        },
        {
            "type": "text",
            "value": f"{float(otc['percent_change']):+.2f}%",
            "x": 204,
            "y": 452,
            "fontSize": 24,
            "fill": get_color(otc["percent_trend"])
        }
    ])

    return output

def transform2(data):
    output = []
    def get_color(trend):
        return "#FE7575" if trend == "up" else "#23ECB1"
    # 預先固定好的 x, y
    positions = {
        "report_date": (48, 57),
        "strong_stocks": [
            {
                "category_name": (77, 168),
                "percent_change": (85, 203),
                "percent_trend": (18, 197)
            },
            {
                "category_name": (77, 298),
                "percent_change": (85, 335),
                "percent_trend": (18, 330)
            },
            {
                "category_name": (77, 430),
                "percent_change": (85, 463),
                "percent_trend": (18, 458)
            }
        ],
        "weak_stocks": [
            {
                "category_name": (227, 168), #22
                "percent_change": (237, 203), #27
                "percent_trend": (170, 197)
            },
            {
                "category_name": (227, 298),
                "percent_change": (237, 335),
                "percent_trend": (170, 330)
            },
            {
                "category_name": (227, 430),
                "percent_change": (237, 463),
                "percent_trend": (170, 458)
            }
        ]
    }

    # report_date
    output.append({
        "type": "text",
        "value": data["report_date"],
        "x": positions["report_date"][0],
        "y": positions["report_date"][1],
        "fontSize": 22,
        "fill": "#73CCFF"
    })

    def add_stock_info(stock_list, position_list):
        for stock, pos in zip(stock_list, position_list):
            color = get_color(stock["percent_trend"])
            output.append({
                "type": "text",
                "value": stock["category_name"],
                "x": pos["category_name"][0],
                "y": pos["category_name"][1],
                "fontSize": 22,
                "fill": "#FFFFFF"
            })
            output.append({
                "type": "text",
                "value": stock["percent_change"],
                "x": pos["percent_change"][0],
                "y": pos["percent_change"][1],
                "fontSize": 27,
                "fill": color
            })
            output.append({
                "type": "image",
                "value": stock["percent_trend"],
                "x": pos["percent_trend"][0],
                "y": pos["percent_trend"][1],
                "fontSize": None,
                "height": None
            })

    add_stock_info(data.get("strong_stocks", []), positions["strong_stocks"])
    add_stock_info(data.get("weak_stocks", []), positions["weak_stocks"])

    return output


def transform4(news_data):
    output = []

    # 正常位置座標
    positions = [
        {"content": (22, 111), "source": (67, 98)},
        {"content": (22, 223), "source": (67, 207)},
        {"content": (22, 337), "source": (67, 322)},
        {"content": (22, 457), "source": (67, 440)}
    ]

    # NA 資料專用位置座標（只需 content）
    na_positions = [
        {"content": (600, 100)},
        {"content": (600, 180)},
        {"content": (600, 260)},
        {"content": (600, 340)}
    ]

    # 補滿到 4 則
    while len(news_data) < 4:
        news_data.append({"content": "NA", "source": None})

    for idx in range(4):
        item = news_data[idx]
        is_na = item.get("content") == "NA"

        if is_na:
            # NA：只輸出 content，用 na_positions
            pos = na_positions[idx]
            output.append({
                "type": "text",
                "value": "NA",
                "x": pos["content"][0],
                "y": pos["content"][1],
                "fontSize": 22,
                "fill": "#FFFFFF"
            })
        else:
            # 正常資料：輸出 content 和 source
            pos = positions[idx]

            output.append({
                "type": "text",
                "value": item["content"],
                "x": pos["content"][0],
                "y": pos["content"][1],
                "fontSize": 22,
                "fill": "#FFFFFF"
            })

            if item.get("source"):
                output.append({
                    "type": "text",
                    "value": item["source"],
                    "x": pos["source"][0],
                    "y": pos["source"][1],
                    "fontSize": 19,
                    "fill": "#000079"
                })

    return output























# def transform5(articles):
#     TITLE_POSITION = {"x": 160, "y": 128, "fontSize": 32, "fill": "#FFFFFF"}
#     SUMMARY_POSITIONS = [
#         {"x": 34, "y": 215, "fontSize": 20, "fill": "#FFFFFF"},
#         {"x": 34, "y": 313, "fontSize": 20, "fill": "#FFFFFF"},
#         {"x": 34, "y": 409, "fontSize": 20, "fill": "#FFFFFF"},
#     ]

#     result = []
#     for article in articles:
#         entry = {
#             "image_index": 0,
#             "data": []
#         }

#         # 固定位置加入 title
#         entry["data"].append({
#             "type": "text",
#             "value": article["title"],
#             **TITLE_POSITION
#         })

#         # 固定位置加入每個 summary（最多三個）
#         for i, summary_item in enumerate(article["summary"][:3]):
#             position = SUMMARY_POSITIONS[i]
#             entry["data"].append({
#                 "type": "text",
#                 "value": summary_item["summary"],
#                 **position
#             })

#         result.append(entry)

#     return result











import os
import pandas as pd
import requests
from datetime import datetime
from pydantic import BaseModel, Field
from typing import List, Dict, Any, Tuple, Optional
import re

class StockAnalysis(BaseModel):
    has_stock: bool = Field(..., description="判斷是否有匹配到股票")
    url: str = Field(..., description="匹配到的股票的URL，找不到則返回空字符串")


# def get_xlsx_data(region: str = "台灣") -> str:
#     """
#     讀取資料夾中的 CSV 文件並返回篩選後的字符串
    
#     Args:
#         region (str): 要返回的區域資料，可選 "台灣" 或 "美國"
        
#     Returns:
#         str: 篩選後的CSV內容字符串
#     """
#     try:
#         base_dir = "/home/<USER>/ai_env/sam/project/stock_img/daily_data/"
#         if not os.path.exists(base_dir):
#             print(f"Base directory not found: {base_dir}")
#             return ""
        
#         current_date = datetime.now().strftime("%Y%m%d")
#         data_dir = os.path.join(base_dir, current_date)
        
#         if not os.path.exists(data_dir):
#             date_dirs = [d for d in os.listdir(base_dir) 
#                          if os.path.isdir(os.path.join(base_dir, d)) and d.isdigit() and len(d) == 8]
#             if date_dirs:
#                 latest_date = max(date_dirs)
#                 data_dir = os.path.join(base_dir, latest_date)
#                 print(f"Using latest date directory: {latest_date}")
#             else:
#                 print("No valid date directories found")
#                 return ""
        
#         csv_files = [f for f in os.listdir(data_dir) if f.endswith('.csv')]
#         if not csv_files:
#             print("No CSV files found")
#             return ""
        
#         file_path = os.path.join(data_dir, csv_files[0])
#         print(f"Reading CSV file: {file_path}")
        
#         # 直接讀取文件內容為字符串
#         try:
#             with open(file_path, 'r', encoding='utf-8') as file:
#                 content = file.read()
#         except UnicodeDecodeError:
#             try:
#                 with open(file_path, 'r', encoding='big5') as file:
#                     content = file.read()
#             except:
#                 with open(file_path, 'r', encoding='cp950') as file:
#                     content = file.read()
        
#         # 按行分割並篩選
#         lines = content.strip().split('\n')
#         filtered_lines = []
        
#         for line in lines:
#             if not line.strip():  # 跳過空行
#                 continue
                
#             # 分割CSV行（考慮到可能有引號包含的逗號）
#             parts = []
#             current_part = ""
#             in_quotes = False
            
#             for char in line:
#                 if char == '"':
#                     in_quotes = not in_quotes
#                 elif char == ',' and not in_quotes:
#                     parts.append(current_part.strip())
#                     current_part = ""
#                     continue
#                 current_part += char
            
#             if current_part:
#                 parts.append(current_part.strip())
            
#             # 確保至少有4個部分（日期、區域、空列、標題）
#             if len(parts) < 4:
#                 continue
                
#             line_region = parts[1].strip()  # 第二個欄位是區域
#             title = parts[3].strip()        # 第四個欄位是標題
            
#             # 篩選條件：
#             # 1. 區域符合要求
#             # 2. 標題不包含"KGI語音精華"（整筆資料都不要）
#             if line_region == region and "KGI語音精華" not in title:
#                 filtered_lines.append(line)
        
#         result = '\n'.join(filtered_lines)
#         print(f"Filtered {len(filtered_lines)} lines for region: {region}")
        
#         return result
        
#     except Exception as e:
#         print(f"Error reading CSV file: {e}")
#         return ""
def get_xlsx_data(region: str = "台灣") -> str:
    """
    讀取資料夾中的 CSV 文件並返回篩選後的字符串
    只保留：日期、區域、股票名稱、URL 四個欄位
    
    Args:
        region (str): 要返回的區域資料，可選 "台灣" 或 "美國"
        
    Returns:
        str: 篩選後的CSV內容字符串
    """
    try:
        base_dir = "/home/<USER>/ai_env/sam/project/stock_img/daily_data/"
        if not os.path.exists(base_dir):
            print(f"Base directory not found: {base_dir}")
            return ""
        
        current_date = datetime.now().strftime("%Y%m%d")
        data_dir = os.path.join(base_dir, current_date)
        
        if not os.path.exists(data_dir):
            date_dirs = [d for d in os.listdir(base_dir) 
                         if os.path.isdir(os.path.join(base_dir, d)) and d.isdigit() and len(d) == 8]
            if date_dirs:
                latest_date = max(date_dirs)
                data_dir = os.path.join(base_dir, latest_date)
                print(f"Using latest date directory: {latest_date}")
            else:
                print("No valid date directories found")
                return ""
        
        csv_files = [f for f in os.listdir(data_dir) if f.endswith('.csv')]
        if not csv_files:
            print("No CSV files found")
            return ""
        
        file_path = os.path.join(data_dir, csv_files[0])
        print(f"Reading CSV file: {file_path}")
        
        # 直接讀取文件內容為字符串
        try:
            with open(file_path, 'r', encoding='utf-8') as file:
                content = file.read()
        except UnicodeDecodeError:
            try:
                with open(file_path, 'r', encoding='big5') as file:
                    content = file.read()
            except:
                with open(file_path, 'r', encoding='cp950') as file:
                    content = file.read()
        
        # 按行分割並篩選
        lines = content.strip().split('\n')
        filtered_lines = []
        
        for line in lines:
            if not line.strip():  # 跳過空行
                continue
                
            # 分割CSV行（考慮到可能有引號包含的逗號）
            parts = []
            current_part = ""
            in_quotes = False
            
            for char in line:
                if char == '"':
                    in_quotes = not in_quotes
                elif char == ',' and not in_quotes:
                    parts.append(current_part.strip())
                    current_part = ""
                    continue
                current_part += char
            
            if current_part:
                parts.append(current_part.strip())
            
            # 確保至少有足夠的欄位
            if len(parts) < 4:  # 至少需要4個欄位
                continue
            
            # 檢查是否包含要排除的內容
            if "KGI語音精華" in line:
                continue
                
            # 提取需要的欄位
            date = parts[0].strip()           # 日期 (第1欄)
            line_region = parts[1].strip()    # 區域 (第2欄)
            url = parts[-1].strip()           # URL (最後一欄)
            
            # 從第5欄開始到倒數第2欄，收集所有股票名稱
            stock_names = []
            for i in range(4, len(parts)-1):  # 從第5欄到倒數第2欄
                if parts[i].strip():
                    stock_names.append(parts[i].strip())
            
            # 如果沒有股票名稱，跳過這筆資料
            if not stock_names:
                continue
                
            stock_name = ", ".join(stock_names)  # 用逗號分隔多個股票名稱
            
            # 篩選條件：區域符合要求且股票名稱不為空
            if line_region == region and stock_name:
                # 重新組合成CSV格式，只保留4個欄位
                filtered_line = f"{date},{line_region},{stock_name},{url}"
                filtered_lines.append(filtered_line)
        
        result = '\n'.join(filtered_lines)
        print(f"Filtered {len(filtered_lines)} lines for region: {region}")
        print("Output format: 日期,區域,股票名稱,URL")
        
        return result
        
    except Exception as e:
        print(f"Error reading CSV file: {e}")
        return ""

def analyze_article_with_llm(article: Dict[str, Any], xlsx_data: str, llm_instance) -> Tuple[int, str]:
    """使用 LLM 分析文章並返回 image_index 和 url"""
    title = article.get('title', '')
    summaries = article.get('summary', [])
    summary_text = ' '.join([s.get('summary', '') for s in summaries])


    print(f"Analyzing article: {title[:50]}...")
    
    # 先分析標題
    title_prompt = f"""
    標題: {title}
    
    要查找的資料 :
    {xlsx_data}
    
    步驟 :
    1. 先判斷標題當中有沒有出現台股(要出現台股的名稱或簡稱才算，例如台積，只出現產業類別不算)
    2. 如果標題沒有出現台股，直接返回 {{"has_stock":false,"url":""}}
    3. 如果標題中有出現台股，則再根據該股票與要查找的資料進行比對，看當中有沒有要查找的股票
        - 如果有且配對到一個，則返回 {{"has_stock":true,"url":"配對到的url"}}，如果同時配對到多個，則使用最新的那則並返回 {{"has_stock":true,"url":"配對到的url"}}
        - 如果沒有，則直接返回 {{"has_stock":false,"url":""}}
        
    配對規則 :
        1. 根據找到的台股，在要查找的資料當中找到相同的台股才算配對成功
        2. 即使出現相同的關鍵字例如 AI ，但如果沒有辦法匹配到相同美股，那就不算匹配成功，匹配成功的例子例如 台積電 匹配到 台積、台積電之類的。
    """

    title_messages = [{"role": "user", "content": title_prompt}]
    
    try:
        title_analysis = llm_instance.chat_json(title_messages)
        print(f"Title analysis result: has_stock={title_analysis.has_stock}, url='{title_analysis.url}'")
        
        if title_analysis.has_stock and title_analysis.url:
            print("Found matching stock in Excel via title, using image_index 0")
            return 0, title_analysis.url
        
    except Exception as e:
        print(f"Error in title analysis: {e}")
    
    # 如果標題沒有找到，分析摘要
    if summary_text.strip():
        summary_prompt = f"""
        摘要: {title}
    
        要查找的資料 :
        {xlsx_data}
        
        步驟 :
        1. 先判斷摘要當中有沒有出現台股(要出現台股的名稱或簡稱才算，例如台積，只出現產業類別不算)
        2. 如果摘要沒有出現台股，直接返回 {{"has_stock":false,"url":""}}
        3. 如果摘要中有出現台股，則再根據該股票與要查找的資料進行比對，看當中有沒有要查找的股票
            - 如果有且配對到一個，則返回 {{"has_stock":true,"url":"配對到的url"}}，如果同時配對到多個，則使用最新的那則並返回 {{"has_stock":true,"url":"配對到的url"}}
            - 如果沒有，則直接返回 {{"has_stock":false,"url":""}}
            
        配對規則 :
            1. 根據找到的台股，在要查找的資料當中找到相同的台股才算配對成功
            2. 即使出現相同的關鍵字例如 AI ，但如果沒有辦法匹配到相同美股，那就不算匹配成功，匹配成功的例子例如 台積電 匹配到 台積、台積電之類的。
        """

        summary_messages = [{"role": "user", "content": summary_prompt}]
        
        try:
            summary_analysis = llm_instance.chat_json(summary_messages)
            print(f"Summary analysis result: has_tw_stock={summary_analysis.has_stock}, url='{summary_analysis.url}'")
            
            if summary_analysis.has_stock and summary_analysis.url:
                print("Found matching stock in Excel via summary, using image_index 0")
                return 0, summary_analysis.url
            
        except Exception as e:
            print(f"Error in summary analysis: {e}")
    
    # 都沒有找到匹配的台股，使用原始文章URL
    print("No Taiwan stock found or no matching data in Excel, using image_index 1")
    return 1, article.get('url', '')


def transform5(articles, OPENAI_API_KEY):
    """主要的轉換函數"""
    TITLE_POS = {"x": 150, "y": 128, "fontSize": 32, "fill": "#FFFFFF"}
    SUMMARY_POS = [
        {"x": 34, "y": 198, "fontSize": 20, "fill": "#FFFFFF"},
        {"x": 34, "y": 303, "fontSize": 20, "fill": "#FFFFFF"},
        {"x": 34, "y": 409, "fontSize": 20, "fill": "#FFFFFF"},
    ]
    
    try:
        llm = OpenAiLLM(model_name="o3", api_key=OPENAI_API_KEY, response_model=StockAnalysis)
    except ImportError:
        print("Warning: OpenAiLLM not imported, you need to implement your LLM interface")
        return [], []
    
    xlsx_data = get_xlsx_data(region="台灣")
    result, result_urls = [], []

    print(f"Processing {len(articles)} articles...")
    
    for i, article in enumerate(articles):
        print(f"\nProcessing article {i+1}/{len(articles)}")
        
        try:
            idx, url = analyze_article_with_llm(article, xlsx_data, llm)
            
            entry = {"image_index": idx, "data": []}
            entry["data"].append({
                "type": "text", 
                "value": article.get("title", ""), 
                **TITLE_POS
            })
            
            # 添加摘要
            for j, s in enumerate(article.get("summary", [])[:3]):
                entry["data"].append({
                    "type": "text", 
                    "value": s.get("summary", ""), 
                    **SUMMARY_POS[j]
                })
            
            result.append(entry)
            result_urls.append(url)
            
            print(f"Article processed: image_index={idx}, url={url[:50] if url else 'None'}...")
            
        except Exception as e:
            print(f"Error processing article {i+1}: {e}")
            # 如果出錯，使用默認值
            entry = {"image_index": 1, "data": []}
            entry["data"].append({
                "type": "text", 
                "value": article.get("title", ""), 
                **TITLE_POS
            })
            for j, s in enumerate(article.get("summary", [])[:3]):
                entry["data"].append({
                    "type": "text", 
                    "value": s.get("summary", ""), 
                    **SUMMARY_POS[j]
                })
            result.append(entry)
            result_urls.append(article.get('url', ''))

    print(f"\nProcessing completed. Results: {len(result)} entries")
    return result, result_urls


def taiex1(lines, OPENAI_API_KEY):
    if not lines:
        return JSONResponse(status_code=400, content={"error": "File is empty."})

    first_line = lines[0]

    match = re.search(r"\b(\d{4})-(\d{1,2})-(\d{1,2})\b", first_line)
    if not match:
        return JSONResponse(status_code=400, content={"error": "Date not found in first line."})

    # 解析日期
    year, month, day = match.groups()
    current_txt_date = f"{year}-{int(month):02d}-{int(day):02d}"
    date_obj = datetime.strptime(current_txt_date, "%Y-%m-%d")
    previous_report_date = (date_obj - timedelta(days=1)).strftime("%Y-%m-%d")
    previous_date = (date_obj - timedelta(days=2)).strftime("%Y-%m-%d")

    # 初始化模型（假設 OpenAiLLM 已定義）
    TAIEX = OpenAiLLM(model_name="gpt-4o", api_key=OPENAI_API_KEY)
    OTC = OpenAiLLM(model_name="gpt-4o", api_key=OPENAI_API_KEY)
    summary = OpenAiLLM(model_name="gpt-4o", api_key=OPENAI_API_KEY, response_model=StockIndex)

    # 獲取前日資料（假設 get_taiex/get_otc 已定義）
    taiex_info = get_taiex(previous_date)
    otc_info = get_otc(previous_date)

    # 取得各自模型回應
    taiex_response = TAIEX.chat([
        {"role": "user", "content": GET_TAIEX.format(date=previous_date, data=taiex_info)}
    ])
    otc_response = OTC.chat([
        {"role": "user", "content": GET_OTC.format(date=previous_date, data=otc_info)}
    ])

    # 整合過去資料並交給 summary 模型
    previous_data = taiex_response + "\n" + otc_response
    
    messages = [
        {
            "role": "user",
            "content": STOCK_INDEX.format(
                current_date=current_txt_date,
                previous_report_date=previous_report_date,
                data=lines,
                previous_data=previous_data
            )
        }
    ]
    result = summary.chat_json(messages)
    result = transform1(result.model_dump())

    return [result]


def taiex2(lines, OPENAI_API_KEY):
    if not lines:
        return JSONResponse(status_code=400, content={"error": "File is empty."})

    Sector = OpenAiLLM(model_name="gpt-4o", api_key=OPENAI_API_KEY, response_model=StrongWeekStack)
    result = Sector.chat_json([
        {"role": "user", "content": Sector_prompt.format(data=lines) }
    ])
    
    result = result.model_dump()
    result = transform2(result)
    return [result]

# 'x':157,'y':279,'fontSize': 20, 'fill': '#FFFFFF'
# def taiex3(docs_text):
#     import re

#     # Helper function: 擷取區段
#     def extract_between(text, start_kw, end_kw):
#         try:
#             start = text.index(start_kw) + len(start_kw)
#             end = text.index(end_kw)
#             return text[start:end].strip()
#         except ValueError:
#             return ''

#     # 擷取兩段
#     part1 = extract_between(docs_text, "昨日台股概況：", "最新美股走勢")
#     part2 = extract_between(docs_text, "台股盤面結構：", "今日焦點股")

#     # 合併文字
#     result = part1 + part2

#     return [[{
#         'type': 'text',
#         'value': result,
#         'x': 157,
#         'y': 279,
#         'fontSize': 20,
#         'fill': '#FFFFFF'
#     }]]
def taiex3(docs_text):
    import re

    # Helper function: 擷取單段
    def extract_between(text, start_kw, end_kw):
        try:
            start = text.index(start_kw) + len(start_kw)
            end = text.index(end_kw)
            return text[start:end].strip()
        except ValueError:
            return ''

    # 擷取各段落
    overview = extract_between(docs_text, "昨日台股概況：", "美股走勢方面：")
    tech_part = extract_between(docs_text, "台股技術面部分：", "台股各類股漲跌：")
    sector_part = extract_between(docs_text, "台股各類股漲跌：", "最後，今日重點個股：")

    # 組成目標格式
    combined_text = f"{overview}\n[台股技術面] {tech_part}\n[台股各類股漲跌] {sector_part}"

    return [[{
        'type': 'text',
        'value': combined_text,
        'x': 20,
        'y': 84,
        'fontSize': 22,
        'fill': '#FFFFFF'
    }]]

class ChangeLineTitleModel(BaseModel):
    title: str = Field(..., description="換行後的標題")    

# def taiex4(lines, OPENAI_API_KEY):
#     def make_title_llm():
#         return OpenAiLLM(
#             model_name="gpt-4o",
#             api_key=OPENAI_API_KEY,
#             response_model=TitleModel
#         )
        
#     shorten_prompt_tpl = (
#         "請將以下標題縮短至 16 字以內，儘量接近或剛好 16 字，不要加入多餘文字，\n"
#         "並以相同 JSON 格式回傳：{{'title': '...' }}\n"
#         "標題：『{title}』"
#     )
#     import re

#     if not lines:
#         return JSONResponse(status_code=400, content={"error": "File is empty."})

#     lines = [line.strip() for line in lines if line.strip() != '']

#     SUMMARY_LLM = OpenAiLLM(model_name="gpt-4o", api_key=OPENAI_API_KEY)
    
#     # 廠商名稱對照表（如果有需要標準化可以加）
#     source_mapping = {
#         "經濟": "經濟日報",
#         "工商": "工商時報",
#         "自由": "自由時報",
#         "聯合": "聯合報",
#         "中時": "中國時報"
#     }

#     result = []

#     # 1. 找出新聞摘要的位置
#     try:
#         start_idx = next(i for i, line in enumerate(lines) if '新聞摘要' in line)
#     except StopIteration:
#         return [{"content": "NA", "source": None} for _ in range(4)]

#     # 2. 從 start_idx+1 開始取資料，直到遇到"加權指數"
#     for line in lines[start_idx + 1:]:
#         if '加權指數' in line:
#             break
            
        
#         # 檢查括號格式：xxx（來源）
#         match = re.search(r'\(([^)]+)\)', line)
#         if match:
#             source = match.group(1).strip()
#             content = line[:match.start()].strip()

#             # 處理 content：若開頭不是中英文數字，清掉首字元
#             while content and not re.match(r'^[\u4e00-\u9fffA-Za-z0-9]', content[0]):
#                 content = content[1:].strip()

#             # 處理 content：移除冒號（但保留前綴字）
#             content = content.replace('：', '').replace(':', '')
#             # 呼叫 summary() 若超過 20 字元
#             # if len(content) > 23:
#             #     # content = summary(content)
#             #     content = SUMMARY_LLM.chat([
#             #         {"role": "user", "content": "你是資料總結助手，請將以下內容總結成不超過 16 個字的摘要，並且絕對只能返回總結的內容，其餘東西都不返回。要總結的內容如下，請勿比總結不超過16字元：\n" + content}
#             #     ])
#             if len(content) > 22:
#                 shorten_prompt = shorten_prompt_tpl.format(title=content)
#                 shorten_resp = make_title_llm().chat_json([{"role": "user", "content": shorten_prompt}])
#                 content = shorten_resp.model_dump().get('title', content)

#             # 若來源簡稱需映射完整名稱
#             source_full = source_mapping.get(source, source)

#             result.append({"content": content, "source": source_full})

#         if len(result) == 4:
#             break

#     # 3. 若不足 4 筆補 NA
#     while len(result) < 4:
#         result.append({"content": "NA", "source": None})

#     result_image = transform4(result)
#     return result ,[result_image]
def extract_news_summary(text_list):
    source_mapping = {
        "經濟": "經濟日報",
        "工商": "工商時報",
        "自由": "自由時報",
        "聯合": "聯合報",
        "中時": "中國時報"
    }
    
    result = []
    start_collecting = False
    
    for text in text_list:
        if "新聞摘要" in text and not start_collecting:
            start_collecting = True
            continue
        
        if start_collecting:
            if "加權指數" in text:
                break

            if text.strip() and text.strip() != '\n':
                processed_text = text.strip()

                while processed_text and not (processed_text[0].isalnum() or '\u4e00' <= processed_text[0] <= '\u9fff'):
                    processed_text = processed_text[1:]

                source_match = re.search(r'\(([^)]+)\)', processed_text)
                if source_match:
                    source_key = source_match.group(1)
                    source_full = source_mapping.get(source_key, source_key)

                    content = re.sub(r'\([^)]+\)', '', processed_text).strip()
                    
                    result.append({
                        "content": content,
                        "source": source_full
                    })
    
    return result


def taiex4(lines, OPENAI_API_KEY):
    import re
    
    def make_title_llm():
        return OpenAiLLM(
            model_name="o3",
            api_key=OPENAI_API_KEY,
            response_model=TitleModel
        )
    
    def extract_first_date(lines):
        """從 lines 中提取第一個符合 YYYY-MM-DD 或 YYYY-M-DD 格式的日期"""
        # 正則表達式匹配 YYYY-MM-DD 或 YYYY-M-DD 格式
        date_pattern = r'\b(\d{4})-(\d{1,2})-(\d{1,2})\b'
        
        for line in lines:
            match = re.search(date_pattern, line)
            if match:
                return match.group(0)  # 返回完整的日期字符串
        
        return None  # 如果沒有找到日期，返回 None
        
    shorten_prompt = "將本新聞標題簡化，約16字左右，並依照指定格式回傳"   
    
    news_infos = extract_news_summary(lines)
    for info in news_infos:
        info['content'] = re.sub(r'\s{2,}', ' ', info['content'])
        if len(info['content']) > 25 :
            shorten_title = make_title_llm().chat_json([{"role": "user", "content": shorten_prompt+f"\n新聞標題:{info['content']}"}])
            content = shorten_title.model_dump().get('title', info['content'])
            info['content'] = content

    result_image = transform4(news_infos)
    
    first_date = extract_first_date(lines)
    if first_date:
        date_item = {
            "type": "text",
            "value": first_date,
            "x": 110,
            "y": 51,
            "fontSize": 14,
            "fill": "#73CCFF"
        }
        result_image.append(date_item)
        
    return news_infos ,[result_image]

# async def taiex5(to_crawl_taiex5, OPENAI_API_KEY):
#     import opencc
    
#     # 初始化繁體中文轉換器
#     converter = opencc.OpenCC('s2t')  # 簡體轉繁體
    
#     shorten_prompt_tpl = (
#         "請將以下標題縮短至 16 字以內，儘量接近或剛好 16 字，不要加入多餘文字，\n"
#         "並以相同 JSON 格式回傳：{{'title': '...' }}\n"
#         "標題：『{title}』"
#     )
    
#     # 縮短摘要的提示模板
#     shorten_summary_tpl = (
#         "請將以下摘要縮短至不超過24個字元（包含標點符號），保留核心意思，\n"
#         "並以繁體中文回答。請直接返回縮短後的摘要，不要加入其他文字。\n"
#         "摘要：『{summary}』"
#     )
    
#     results_for_user = []

#     # 初始化 LLM 實例
#     summary_llm = OpenAiLLM(
#         model_name="gpt-4o",
#         api_key=OPENAI_API_KEY,
#         response_model=US_NEWS_SUM
#     )

#     shorten_title_llm = OpenAiLLM(
#         model_name="gpt-4o",
#         api_key=OPENAI_API_KEY,
#         response_model=TitleModel
#     )
    
#     # 用於縮短摘要的 LLM
#     shorten_summary_llm = OpenAiLLM(
#         model_name="gpt-4o",
#         api_key=OPENAI_API_KEY
#     )

#     for item in to_crawl_taiex5:
#         content = item['content']
#         source = item['source']

#         # 使用 search 查找 URL
#         search_results = search_first_url(content, wanted_source=source)
#         if not search_results:
#             continue

#         search_item = search_results[0]  # 取第一筆
#         url = search_item['url']
#         final_title = content

#         # 下載網頁內容
#         md = await crawl_content(url)
#         if md:
#             summary_resp = summary_llm.chat_json([
#                 {
#                     "role": "user",
#                     "content": f"""
#                  請依據下方新聞標題與新聞內容，條列出這則新聞的三個重點句子。

# 【生成限制】：
# 1. 每個重點**必須是完整語句**，清楚描述某個事件或狀況的「起因」與「結果」。
# 2. **不得重複或抄襲新聞標題內容的字詞**，請改寫成不同表達。
# 3. 每個重點句子**不得超過24個字元**（中英文與符號皆算一字），**但應盡量接近24字元**。
# 4. 每個句子應強調「某事發生後，造成什麼結果、變化或影響」。
# 5. 每個句子需具備明確的「主詞 + 動作 + 結果」。


# 新聞標題：
# {final_title}

# 新聞內容：
# {md}


#                  """
#                 }
#             ])
#             summary = summary_resp.model_dump().get("summaries", [])
            
#             # 檢查每個摘要的字數，如果超過24個字元，則縮短
#             processed_summary = []
#             for s in summary:
#                 summary_text = s.get("summary", "")
#                 # 確保是繁體中文
#                 summary_text = converter.convert(summary_text)
                
#                 # 檢查字數
#                 if len(summary_text) > 24:
#                     # 使用 AI 幫忙縮減
#                     shorten_prompt = shorten_summary_tpl.format(summary=summary_text)
#                     shortened_text = shorten_summary_llm.chat([
#                         {"role": "user", "content": shorten_prompt}
#                     ])
#                     # 確保縮短後的文字也是繁體中文
#                     shortened_text = converter.convert(shortened_text)
#                     # 如果縮短後仍然超過24個字元，則截斷
#                     if len(shortened_text) > 24:
#                         shortened_text = shortened_text[:24]
#                     processed_summary.append({"summary": shortened_text})
#                 else:
#                     processed_summary.append({"summary": summary_text})
            
#             summary = processed_summary
#         else:
#             summary = []

#         # 縮短標題（如果太長）
#         if len(final_title) > 16:
#             shorten_prompt = shorten_prompt_tpl.format(title=final_title)
#             shorten_resp = shorten_title_llm.chat_json([
#                 {"role": "user", "content": shorten_prompt}
#             ])
#             final_title = shorten_resp.model_dump().get('title', final_title)
#             # 確保標題是繁體中文
#             final_title = converter.convert(final_title)


#         CHANGELINE_LLM = OpenAiLLM(model_name="o3", api_key=OPENAI_API_KEY, response_model=ChangeLineTitleModel)
#         final_result = CHANGELINE_LLM.chat_json([{"role": "user", "content": fr"""
#                                                   請根據原標題，將其利用換行符號 \n 分成兩行，並遵守以下規則：
# 請根據原標題，將其利用換行符號 \n 分成兩行，並遵守以下規則：
# 1.每行最多 9 個字（中文一字算 1 個字，英文字母與數字是兩個字算 1 個）。
# 2.最多只能分成兩行。
# 3.剛好 16 個字 時，請在第 9 個字之後強制換行(就算會截斷英文也要換)。
# 4.若標題少於 16 個字，則可在適合的位置換行，只要每行不超過 8 個字。
# 5.移除所有標點符號與特殊符號。
# 6.保留原始文字內容，不更改標題用詞，只處理換行與符號移除。
# 7.請嚴格遵循字數限制，並確保每行的字數符合要求，在遵守字數限制前提下，要挑語句適當的地方換行。
# 8.結果以相同 JSON 格式回傳，例如：
# {{'title': '...' }}

# 原標題：{final_title}
#                                                   """}])
        
#         final_title = final_result.model_dump().get('title', final_title)
#         print(f"final_title: {final_title}")




#         results_for_user.append({
#             'title': final_title,
#             'source': source,
#             'url': url,
#             'summary': summary
#         })
    
#     final_result, result_urls = transform5(results_for_user, OPENAI_API_KEY)
    
#     import json
    
#     combined_results = []
#     for result, image_url in zip(results_for_user, result_urls):
#         combined_results.append({
#             'title': result['title'],
#             'news_url': result['url'],
#             'url': image_url
#         })

#     # 建立目錄 YYYYMMDD
#     date_str = datetime.now().strftime("%Y%m%d")
#     base_dir = "/home/<USER>/ai_env/sam/project/stock_img/urls"
#     target_dir = os.path.join(base_dir, date_str)
#     os.makedirs(target_dir, exist_ok=True)

#     # 存成 JSON
#     file_path = os.path.join(target_dir, "taiex.json")
#     with open(file_path, "w", encoding="utf-8") as f:
#         json.dump(combined_results, f, ensure_ascii=False, indent=4)

#     print(f"JSON saved to {file_path}")
    
#     return [final_result] , result_urls


async def taiex5(to_crawl_taiex5, OPENAI_API_KEY):
    import opencc
    converter = opencc.OpenCC('s2t')
    
    for item in to_crawl_taiex5 :
        search_results = search_first_url(item['content'], wanted_source=item['source'])
        if not search_results:
            continue
        
        search_item = search_results[0]  # 取第一筆
        url = search_item['url']
        item['url'] = url
        # print(f"搜尋 : {item['content']}、Wanted_source: {item['source']}、Searched url : {url}")
        md = await crawl_content(url)
        
        summary_llm = OpenAiLLM(
            model_name="o3",
            api_key=OPENAI_API_KEY,
            response_model=US_NEWS_SUM
        )
        
        if not md:
            return {"message":"can not find news content."}
        
        summary_resp = summary_llm.chat_json([
            {
                "role": "user",
                "content": f"""新聞標題:{item['content']}\n新聞內容:{md}\n將本新聞內容請歸納出3個重點，每個重點約20個字左右，重點描述不要與新聞標題重覆，以及不使用文字加粗方式進行回答，要根據指定格式回覆\n"""
            }
        ])
        
        summary = summary_resp.model_dump().get("summaries", [])
        print(f"新聞標題 : {item['content']}")
        print()
        print(summary)
        item["summary"] = summary
        
        shorten_title_llm = OpenAiLLM(
            model_name="o3",
            api_key=OPENAI_API_KEY,
            response_model=TitleModel
        )
        
        shorten_prompt = "將本新聞標題簡化，約16字左右，並依照指定格式回傳"
        if len(item['content']) > 16 :
            shorten_resp = shorten_title_llm.chat_json(
                [{"role": "user", "content": shorten_prompt+f"\n新聞標題:{item['content']}"}]
            )
            temp_title = shorten_resp.model_dump().get('title', item['content'])
        else :
            temp_title = item['content']
            
        class ChangeLineTitleModel(BaseModel):
            title: str = Field(..., description="換行後的標題")
        
        change_line_prompt = fr"""
        你的任務是將標題進行換行，以下是判斷步驟 :
        1. 先判斷標題是否已經根據語意進行換行
            a. 如果已經換行，則先判斷換行的行數，如果超過兩行，則重新判斷換行位置(因為最多只能兩行)
            b. 如果已經換行且不超過兩行，則判斷每行字數有沒有超過9個字元(中文一字算 1 個字，英文字母與數字是兩個字算 1 個)，如果超過，則直接返回原標題(保留空格，不使用換行符號\n)
            c. 如果還沒換行，則根據語意進行換行，最多兩行，每行不超過9個字元(使用\n進行換行並刪除空格)
        2. 根據指定格式回覆。
        
        原標題 : {temp_title}
        """
        CHANGELINE_LLM = OpenAiLLM(model_name="o3", api_key=OPENAI_API_KEY, response_model=ChangeLineTitleModel)
        final_result = CHANGELINE_LLM.chat_json([{"role":"user","content":change_line_prompt}])
        
        final_title = final_result.model_dump().get('title', temp_title)
        item['title'] = final_title
    
    final_result, result_urls = transform5(to_crawl_taiex5, OPENAI_API_KEY)
    
    import json
    
    combined_results = []
    for result, image_url in zip(to_crawl_taiex5, result_urls):
        combined_results.append({
            'title': result['title'],
            'news_url': result['url'],
            'url': image_url
        })

    # 建立目錄 YYYYMMDD
    date_str = datetime.now().strftime("%Y%m%d")
    base_dir = "/home/<USER>/ai_env/sam/project/stock_img/urls"
    target_dir = os.path.join(base_dir, date_str)
    os.makedirs(target_dir, exist_ok=True)

    # 存成 JSON
    file_path = os.path.join(target_dir, "taiex.json")
    with open(file_path, "w", encoding="utf-8") as f:
        json.dump(combined_results, f, ensure_ascii=False, indent=4)

    print(f"JSON saved to {file_path}")
    
    return [final_result] , result_urls